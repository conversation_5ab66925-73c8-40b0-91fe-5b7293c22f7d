#!/Users/<USER>/.local/share/uv/tools/aider-chat/bin/python3
"""
Test script to verify all dependencies are working correctly
"""

def test_imports():
    """Test all required imports"""
    print("Testing imports...")
    
    try:
        from fyers_apiv3 import fyersModel
        print("✅ fyers_apiv3 imported successfully")
        print(f"   Available classes: {[attr for attr in dir(fyersModel) if not attr.startswith('_')]}")
    except ImportError as e:
        print(f"❌ fyers_apiv3 import failed: {e}")
        return False

    try:
        import pandas as pd
        print("✅ pandas imported successfully")
    except ImportError as e:
        print(f"❌ pandas import failed: {e}")
        return False

    try:
        import pandas_ta
        print("✅ pandas_ta imported successfully")
    except ImportError as e:
        print(f"❌ pandas_ta import failed: {e}")
        return False

    try:
        import plyer
        print("✅ plyer imported successfully")
    except ImportError as e:
        print(f"❌ plyer import failed: {e}")
        return False

    try:
        from telegram import Bot
        print("✅ python-telegram-bot imported successfully")
    except ImportError as e:
        print(f"❌ python-telegram-bot import failed: {e}")
        return False

    return True

def test_fyers_session_creation():
    """Test Fyers session model creation"""
    print("\nTesting Fyers SessionModel creation...")
    
    try:
        from fyers_apiv3 import fyersModel
        
        # Test SessionModel creation (without actual credentials)
        session = fyersModel.SessionModel(
            client_id="TEST-100", 
            secret_key="TEST_SECRET", 
            redirect_uri="https://127.0.0.1", 
            response_type="code", 
            grant_type="authorization_code"
        )
        print("✅ SessionModel created successfully")
        
        # Test FyersModel creation (without actual token)
        fyers = fyersModel.FyersModel(
            token="TEST_TOKEN", 
            is_async=False, 
            client_id="TEST-100", 
            log_path=""
        )
        print("✅ FyersModel created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Fyers session creation failed: {e}")
        return False

def main():
    print("=== Dependency Test for Automated Momentum Trader ===\n")
    
    # Test imports
    imports_ok = test_imports()
    
    # Test Fyers session creation
    session_ok = test_fyers_session_creation()
    
    print("\n=== Test Summary ===")
    if imports_ok and session_ok:
        print("✅ All tests passed! Your dependencies are correctly installed.")
        print("\nNext steps:")
        print("1. Set your environment variables:")
        print("   export FYERS_APP_ID='JMHITXL31I-100'")
        print("   export FYERS_APP_SECRET='58XM4NGA9U'")
        print("   export TRADING_CAPITAL='500000'  # Optional")
        print("2. Run your automated momentum trader:")
        print("   python automated_momentum_trader.py")
    else:
        print("❌ Some tests failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
