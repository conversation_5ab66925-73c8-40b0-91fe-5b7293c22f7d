#!/Users/<USER>/.local/share/uv/tools/aider-chat/bin/python3
"""
Momentum Auto‑Trader
====================
Scans a predefined watch‑list of 25 momentum tickers, checks intraday prices
against user‑defined *entry zones*, and sends BUY (and optional stop/target)
orders through the Fyers API the moment conditions are met.

❖ How it works
---------------
1. **Authentication** – builds or refreshes a Fyers access‑token (saved locally)
   so the script can be run headless after the first manual login.
2. **Market Data Loop** – polls quotes every N seconds (or attach to the
   WebSocket if you prefer) during NSE trading hours.
3. **Rule Engine** – for each symbol, decides whether the price has entered the
   user‑defined entry zone *and* the spread is acceptable.
4. **Position Sizer** – calculates quantity such that the capital at risk
   (difference between entry and stop‑loss) is ≈ 1 % of total equity *and* the
   exposure never exceeds a configurable percentage per trade.
5. **Order Router** – places a limit/market order plus a protective stop (and
   optional target) via Fyers.  Uses `productType=INTRADAY` so the position is
   auto‑squared off at 15:30 IST if you forget.
6. **Logging & Alerts** – writes all activity to `trades.csv` and pushes a
   desktop toast/Telegram message so you can keep tabs while away.

Fill your API keys, tweak the entry dictionary, and just run: `python
momentum_autotrader.py`.
"""
from __future__ import annotations
import os, sys, json, time, signal, logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

import pandas as pd
# pip install fyers-apiv3 pandas_ta plyer python-telegram-bot==13.*
from fyers_apiv3 import fyersModel
from plyer import notification  # desktop toast

################################################################################
# CONFIGURATION                                                                 #
################################################################################

#–– General Trading Parameters ––
CAPITAL = float(os.getenv("TRADING_CAPITAL", "500000"))       # total deployable ₹
MAX_ALLOC_PCT = 0.20   # maximum 20 % of capital per single position
RISK_PCT = 0.01        # risk 1 % of capital per trade (position sizing basis)
BROKERAGE_SLIPPAGE_PCT = 0.0005  # 0.05 % slippage buffer
PRICE_POLL_SECS = 15   # frequency of quote polling

#–– Fyers Credentials ––
APP_ID = os.getenv("FYERS_APP_ID", "JMHITXL31I-100")           # e.g. "YOURAPPID-100"
APP_SECRET = os.getenv("FYERS_APP_SECRET", "58XM4NGA9U")   # secret key
REDIRECT_URI = os.getenv("FYERS_REDIRECT_URI", "http://127.0.0.1:5000/fyers/callback")
TOKEN_PATH = "access_token.txt"              # stores the reusable token

#–– Telegram Alert (optional) ––
TG_BOT_TOKEN = os.getenv("TG_BOT_TOKEN")
TG_CHAT_ID  = os.getenv("TG_CHAT_ID")

#–– Watch‑list of 25 symbols ––
# Fyers format: "NSE:SYMBOL-EQ"
WATCHLIST = [
    "NSE:ARENTERP-EQ", "NSE:RAYMONDLSL-EQ", "NSE:HSCL-EQ", "NSE:GODFRYPHLP-EQ",
    "NSE:SANSTAR-EQ", "NSE:BANG-EQ", "NSE:THOMASCOTT-EQ", "NSE:NACLIND-EQ",
    "NSE:HPIL-EQ", "NSE:SMLISUZU-EQ", "NSE:HUBTOWN-EQ", "NSE:AVROIND-EQ",
    "NSE:HBSL-EQ", "NSE:JKLAKSHMI-EQ", "NSE:SETCO-EQ", "NSE:SABTNL-EQ",
    "NSE:RESPONIND-EQ", "NSE:ORBTEXP-EQ", "NSE:INDRAMEDCO-EQ", "NSE:PFOCUS-EQ",
    "NSE:AKZOINDIA-EQ", "NSE:POCL-EQ", "NSE:SANGAMIND-EQ", "NSE:SAURASHCEM-EQ",
    "NSE:LLOYDSENGG-EQ",
]

#–– Hard‑coded Entry Zones & Risk Parameters (example) ––
# Feel free to generate these on‑the‑fly from your scanner.
ENTRY_RULES: Dict[str, Dict[str, float]] = {
    "NSE:AKZOINDIA-EQ": {"lower": 3365, "upper": 3400, "sl": 3310, "target": 3570},
    "NSE:HSCL-EQ":      {"lower": 495,  "upper": 500,  "sl": 485,  "target": 535},
    "NSE:GODFRYPHLP-EQ":{"lower": 8950, "upper": 9020, "sl": 8760, "target": 9350},
    # … for the rest, default to ±1 % dip zone with 2 × ATR target computed later
}

################################################################################
# UTILITY FUNCTIONS                                                             #
################################################################################

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s",
    datefmt="%H:%M:%S",
    handlers=[logging.FileHandler("autotrader.log"), logging.StreamHandler(sys.stdout)],
)
LOG = logging.getLogger("autotrader")


def send_notification(title: str, msg: str):
    """Desktop + Telegram pushes."""
    try:
        notification.notify(title=title, message=msg, timeout=5)
    except Exception:
        pass
    if TG_BOT_TOKEN and TG_CHAT_ID:
        try:
            from telegram import Bot
            Bot(token=TG_BOT_TOKEN).send_message(chat_id=TG_CHAT_ID, text=f"{title}\n{msg}")
        except Exception as e:
            LOG.warning("Telegram alert failed: %s", e)


################################################################################
# FYERS SESSION                                                                 #
################################################################################

def get_fyers_session() -> fyersModel.FyersModel:
    """Authenticate once; reuse saved access‑token afterwards."""
    if os.path.exists(TOKEN_PATH):
        with open(TOKEN_PATH) as fp:
            access_token = fp.read().strip()
            fyers = fyersModel.FyersModel(token=access_token, is_async=False, client_id=APP_ID, log_path="logs")
            try:
                fyers.get_profile()
                LOG.info("Loaded cached token.")
                return fyers
            except Exception:
                LOG.info("Cached token expired; generating new one…")
    #–– Manual one‑time login ––
    session = fyersModel.SessionModel(
        client_id=APP_ID,
        secret_key=APP_SECRET,
        redirect_uri=REDIRECT_URI,
        response_type="code",
        grant_type="authorization_code"
    )
    # Enhanced authentication with automation features
    auth_url = session.generate_authcode()

    # Check for TOTP automation
    totp_code = None
    totp_secret = os.getenv("FYERS_TOTP_SECRET")
    if totp_secret:
        try:
            import pyotp
            totp = pyotp.TOTP(totp_secret)
            totp_code = totp.now()
            print(f"🔐 Auto-generated TOTP: {totp_code}")
        except ImportError:
            print("💡 Install pyotp for TOTP automation: pip install pyotp")
        except Exception as e:
            print(f"⚠️  TOTP generation failed: {e}")

    print("🚀 AUTOMATED AUTHENTICATION")
    print("=" * 40)
    print(f"🌐 Auth URL: {auth_url}")
    if totp_code:
        print(f"📱 TOTP Code: {totp_code} (auto-generated)")
        print("⏰ TOTP expires in 30 seconds")

    # Auto-open browser
    try:
        import webbrowser
        webbrowser.open(auth_url)
        print("🌐 Browser opened automatically")
    except:
        print("⚠️  Could not auto-open browser")

    print("\n📋 Quick Steps:")
    print("1. Login in the opened browser")
    if totp_code:
        print(f"2. Enter TOTP: {totp_code}")
    print("3. Copy redirected URL and paste below")

    print("» Open this URL, login & paste the redirect URL here:\n", auth_url)
    redirected = input("Redirected URL: ")
    session.set_token(redirected)
    response = session.generate_token()
    access_token = response["access_token"]
    with open(TOKEN_PATH, "w") as fp:
        fp.write(access_token)
    LOG.info("New token saved.")
    return fyersModel.FyersModel(token=access_token, is_async=False, client_id=APP_ID, log_path="logs")


FYERS = get_fyers_session()

################################################################################
# MARKET DATA & RULE EVALUATION                                                 #
################################################################################

def market_time_now() -> datetime:
    return datetime.now(tz=datetime.now().astimezone().tzinfo)

def is_market_open() -> bool:
    now = market_time_now().time()
    return now >= datetime.strptime("09:15", "%H:%M").time() and now <= datetime.strptime("15:30", "%H:%M").time()


def fetch_quotes(symbols: List[str]) -> Dict[str, Any]:
    """Fetch LTP + bid/ask."""
    symbols_str = ",".join(symbols)
    data = FYERS.quotes({"symbols": symbols_str})
    if data["code"] != 200:
        raise RuntimeError(data)
    return {d["n"]: d for d in data["d"]}


def should_enter(symbol: str, q: Dict[str, Any]) -> bool:
    rule = ENTRY_RULES.get(symbol)
    ltp = q["lp"]  # last traded price
    if not rule:
        # Dynamically derive a +-1% dip entry unless existing position.
        lower, upper = ltp * 0.989, ltp * 0.996  # example tweak
    else:
        lower, upper = rule["lower"], rule["upper"]
    # Check if bid/ask data is available, otherwise skip spread check
    if "bid" in q and "ask" in q:
        spread_ok = (q["ask"] - q["bid"]) / ltp < 0.002  # <0.2% spread
    else:
        spread_ok = True  # Skip spread check if data not available
    in_zone = lower <= ltp <= upper
    return in_zone and spread_ok


def calc_qty(price: float, sl_price: float, cash_available: float) -> int:
    risk_per_share = abs(price - sl_price)
    risk_cap = CAPITAL * RISK_PCT
    qty = int(risk_cap / risk_per_share)
    max_shares_by_cap = int((CAPITAL * MAX_ALLOC_PCT) / price)
    max_shares_by_cash = int(cash_available / price)
    return max(1, min(qty, max_shares_by_cap, max_shares_by_cash))


def place_bracket(symbol: str, entry: float, sl: float, target: float, qty: int):
    body = {
        "symbol": symbol,
        "qty": qty,
        "type": 2,  # 2 = Market
        "side": 1,  # 1 = Buy
        "productType": "INTRADAY",
        "limitPrice": 0,
        "stopPrice": 0,
        "disclosedQty": 0,
        "validity": "DAY",
        "offlineOrder": False,
        "takeProfit": round(target, 2),
        "stopLoss": round(sl, 2),
    }
    resp = FYERS.place_order(body)
    LOG.info("Order response: %s", resp)
    if resp.get("code") == 200:
        send_notification("Entry Executed", f"{symbol} x{qty} @ {entry}")
        with open("trades.csv", "a") as fp:
            fp.write(f"{datetime.now()},{symbol},{qty},{entry},{sl},{target}\n")

################################################################################
# MAIN LOOP                                                                     #
################################################################################

def graceful_exit(signo, _frame):
    LOG.info("Signal %s received – exiting gracefully…", signo)
    sys.exit(0)

for sig in (signal.SIGINT, signal.SIGTERM):
    signal.signal(sig, graceful_exit)


LOG.info("Starting Momentum Auto‑Trader – capital ₹%.0f", CAPITAL)

cash_available = CAPITAL  # simplistic model; update from FYERS funds API if needed

while True:
    if not is_market_open():
        LOG.info("Waiting for market to open…")
        time.sleep(60)
        continue

    try:
        quotes = fetch_quotes(WATCHLIST)
    except Exception as e:
        LOG.warning("Quote fetch failed: %s", e)
        time.sleep(PRICE_POLL_SECS)
        continue

    for sym, q in quotes.items():
        if should_enter(sym, q):
            rule = ENTRY_RULES.get(sym, {})
            ltp = q["lp"]
            sl = rule.get("sl", ltp * 0.97)  # 3% stop default
            target = rule.get("target", ltp * 1.04)  # 4% target default
            qty = calc_qty(ltp, sl, cash_available)
            if qty <= 0:
                LOG.info("%s: No funds for new position", sym)
                continue
            place_bracket(sym, ltp, sl, target, qty)
            cash_available -= qty * ltp * (1 + BROKERAGE_SLIPPAGE_PCT)
            LOG.info("Cash left: ₹%.0f", cash_available)

    time.sleep(PRICE_POLL_SECS)
