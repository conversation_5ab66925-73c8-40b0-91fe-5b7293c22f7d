from flask import Blueprint, render_template, request
from extensions import db
import pandas as pd

predictions_bp = Blueprint('predictions', __name__)

@predictions_bp.route('/predictions', methods=['GET'])
def predictions():
    filter_option = request.args.get('filter', 'TOP 10')
    limit = {
        'TOP 10': 10,
        'TOP 20': 20,
        'TOP 50': 50,
        'TOP 100': 100,
        'ALL': None
    }.get(filter_option, 10)

    try:
        df = pd.read_csv('predictions/aggregate_30day_predictions.csv')
    except FileNotFoundError:
        return "Error: aggregate_30day_predictions.csv not found.", 404

    df = df.sort_values(by='30 day returns', ascending=False)
    
    if limit:
        df = df.head(limit)
    
    df['Rank'] = range(1, len(df) + 1)
    predictions = df.to_dict(orient='records')

    return render_template('predictions.html', predictions=predictions, filter_option=filter_option)
