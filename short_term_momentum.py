import os
import pandas as pd
import numpy as np
import pandas_ta as ta
from sqlalchemy import create_engine
from datetime import datetime, timedelta
from dotenv import load_dotenv
import warnings

warnings.filterwarnings("ignore")
load_dotenv()

# Database setup
current_directory = os.path.dirname(os.path.abspath(__file__))
instance_folder = os.path.join(current_directory, 'instance')
db_file_path = os.path.join(instance_folder, os.getenv('SQLALCHEMY_DATABASE_URI').replace('sqlite:///', ''))
engine = create_engine(f'sqlite:///{db_file_path}', echo=False)

# Ensure predictions folder exists
predictions_folder = os.getenv('PREDICTIONS_FOLDER', 'predictions')
os.makedirs(predictions_folder, exist_ok=True)

def calculate_short_term_indicators(df):
    """Calculate indicators optimized for short-term trading"""
    
    # Short-term moving averages
    df['ema_5'] = ta.ema(df['close'], length=5)
    df['ema_10'] = ta.ema(df['close'], length=10)
    df['ema_20'] = ta.ema(df['close'], length=20)
    
    # VWAP (Volume Weighted Average Price) - key for intraday
    df['vwap'] = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()
    
    # RSI for momentum
    df['rsi'] = ta.rsi(df['close'], length=14)
    
    # MACD for trend changes
    macd = ta.macd(df['close'], fast=12, slow=26, signal=9)
    df['macd'] = macd['MACD_12_26_9']
    df['macd_signal'] = macd['MACDs_12_26_9']
    df['macd_histogram'] = macd['MACDh_12_26_9']
    
    # Volume indicators
    df['volume_sma'] = ta.sma(df['volume'], length=10)
    df['volume_ratio'] = df['volume'] / df['volume_sma']
    
    # Price momentum
    df['price_change_1d'] = df['close'].pct_change(1) * 100
    df['price_change_2d'] = df['close'].pct_change(2) * 100
    df['price_change_3d'] = df['close'].pct_change(3) * 100
    
    # Volatility
    df['atr'] = ta.atr(df['high'], df['low'], df['close'], length=14)
    df['atr_ratio'] = df['atr'] / df['close'] * 100
    
    # Support/Resistance levels
    df['resistance'] = df['high'].rolling(window=20).max()
    df['support'] = df['low'].rolling(window=20).min()
    df['price_position'] = (df['close'] - df['support']) / (df['resistance'] - df['support'])
    
    return df

def calculate_breakout_score(df):
    """Score for breakout potential (0-100)"""
    if len(df) < 20:
        return 0
    
    latest = df.iloc[-1]
    score = 0
    
    # 1. Price vs EMAs (30 points)
    if latest['close'] > latest['ema_5']:
        score += 10
    if latest['close'] > latest['ema_10']:
        score += 10
    if latest['ema_5'] > latest['ema_10']:
        score += 10
    
    # 2. Breakout from resistance (25 points)
    resistance_break = (latest['close'] / latest['resistance'] - 1) * 100
    if resistance_break > -1:  # Near or above resistance
        score += 15
    if resistance_break > 0:  # Above resistance
        score += 10
    
    # 3. Volume confirmation (25 points)
    if latest['volume_ratio'] > 2.0:  # 2x volume
        score += 25
    elif latest['volume_ratio'] > 1.5:
        score += 20
    elif latest['volume_ratio'] > 1.2:
        score += 15
    
    # 4. MACD momentum (20 points)
    if latest['macd'] > latest['macd_signal']:
        score += 10
    if latest['macd_histogram'] > 0:
        score += 5
    if df['macd_histogram'].iloc[-1] > df['macd_histogram'].iloc[-2]:
        score += 5
    
    return min(score, 100)

def calculate_momentum_score(df):
    """Score for price momentum (0-100)"""
    if len(df) < 5:
        return 0
    
    latest = df.iloc[-1]
    score = 0
    
    # Recent price changes
    change_1d = latest['price_change_1d']
    change_2d = latest['price_change_2d'] 
    change_3d = latest['price_change_3d']
    
    # 1-day momentum (40 points)
    if change_1d > 5: score += 40
    elif change_1d > 3: score += 30
    elif change_1d > 1: score += 20
    elif change_1d > 0: score += 10
    
    # 2-day momentum (35 points)
    if change_2d > 8: score += 35
    elif change_2d > 5: score += 25
    elif change_2d > 2: score += 15
    elif change_2d > 0: score += 10
    
    # 3-day momentum (25 points)
    if change_3d > 12: score += 25
    elif change_3d > 8: score += 20
    elif change_3d > 4: score += 15
    elif change_3d > 0: score += 10
    
    # Consistency bonus
    if change_1d > 0 and change_2d > 0:
        score += 10
    
    return min(score, 100)

def calculate_volume_surge_score(df):
    """Score for volume surge (0-100)"""
    if len(df) < 10:
        return 0
    
    latest = df.iloc[-1]
    score = 0
    
    # Current volume vs average
    vol_ratio = latest['volume_ratio']
    if vol_ratio > 5.0: score += 50
    elif vol_ratio > 3.0: score += 40
    elif vol_ratio > 2.0: score += 30
    elif vol_ratio > 1.5: score += 20
    elif vol_ratio > 1.2: score += 10
    
    # Volume trend (last 2 days)
    recent_vol = df['volume'].iloc[-2:].mean()
    prev_vol = df['volume'].iloc[-5:-2].mean()
    vol_trend = (recent_vol / prev_vol - 1) * 100
    
    if vol_trend > 50: score += 30
    elif vol_trend > 25: score += 20
    elif vol_trend > 10: score += 10
    
    # Price-Volume relationship
    if latest['price_change_1d'] > 2 and vol_ratio > 1.5:
        score += 20  # Strong price move with volume
    
    return min(score, 100)

def calculate_risk_reward_score(df):
    """Score for risk-reward setup (0-100)"""
    if len(df) < 20:
        return 50
    
    latest = df.iloc[-1]
    score = 100
    
    # Volatility check
    atr_ratio = latest['atr_ratio']
    if atr_ratio > 8: score -= 30  # Too volatile
    elif atr_ratio > 6: score -= 20
    elif atr_ratio > 4: score -= 10
    elif atr_ratio < 1: score -= 20  # Too low volatility
    
    # RSI position
    rsi = latest['rsi']
    if 40 <= rsi <= 70: score += 0  # Good range
    elif rsi > 80: score -= 25  # Overbought
    elif rsi < 20: score -= 25  # Oversold
    elif rsi > 75: score -= 15
    elif rsi < 25: score -= 15
    
    # Position in range
    price_pos = latest['price_position']
    if 0.7 <= price_pos <= 0.95: score += 0  # Good position
    elif price_pos > 0.95: score -= 15  # Too high
    elif price_pos < 0.3: score -= 10  # Too low
    
    return max(score, 0)

def scan_short_term_momentum():
    """Main function to scan for short-term momentum opportunities"""
    
    print("Starting Short-Term Momentum Scanner (1-3 Days)...")
    print("Focusing on: Breakouts, Volume Surges, Price Momentum")
    
    # Get all symbols from database
    query = "SELECT DISTINCT symbol FROM finance_data"
    symbols_df = pd.read_sql(query, engine)
    symbols = symbols_df['symbol'].tolist()
    
    if not symbols:
        print("No symbols found in database!")
        return
    
    results = []
    processed = 0
    skipped = 0
    errors = 0

    print(f"Total symbols to process: {len(symbols)}")

    for symbol in symbols:
        try:
            # Load recent data (last 50 days is enough for short-term)
            query = f"""
            SELECT date, open, high, low, close, volume 
            FROM finance_data 
            WHERE symbol = '{symbol}' 
            ORDER BY date DESC 
            LIMIT 50
            """
            df = pd.read_sql(query, engine)
            
            if len(df) < 20:  # Need minimum data
                skipped += 1
                continue
            
            # Sort by date ascending for calculations
            df = df.sort_values('date').reset_index(drop=True)
            df['date'] = pd.to_datetime(df['date'])
            
            # Calculate indicators
            df = calculate_short_term_indicators(df)
            
            # Skip penny stocks and low volume stocks
            current_price = df['close'].iloc[-1]
            avg_volume = df['volume'].tail(5).mean()
            
            if current_price < 5 or avg_volume < 5000:
                skipped += 1
                continue
            
            # Calculate scores
            breakout_score = calculate_breakout_score(df)
            momentum_score = calculate_momentum_score(df)
            volume_score = calculate_volume_surge_score(df)
            risk_reward_score = calculate_risk_reward_score(df)
            
            # Calculate composite score for short-term trading
            composite_score = (
                breakout_score * 0.30 +     # 30% breakout potential
                momentum_score * 0.35 +     # 35% price momentum
                volume_score * 0.25 +       # 25% volume surge
                risk_reward_score * 0.10    # 10% risk management
            )
            
            # Only include stocks with decent scores
            if composite_score < 40:
                skipped += 1
                continue
            
            # Get key metrics
            latest = df.iloc[-1]
            
            results.append({
                'Symbol': symbol,
                'Composite_Score': round(composite_score, 1),
                'Breakout_Score': round(breakout_score, 1),
                'Momentum_Score': round(momentum_score, 1),
                'Volume_Score': round(volume_score, 1),
                'Risk_Reward_Score': round(risk_reward_score, 1),
                'Current_Price': round(current_price, 2),
                'Change_1D': round(latest['price_change_1d'], 2),
                'Change_2D': round(latest['price_change_2d'], 2),
                'Change_3D': round(latest['price_change_3d'], 2),
                'Volume_Ratio': round(latest['volume_ratio'], 2),
                'RSI': round(latest['rsi'], 1),
                'Price_vs_EMA5': round(((latest['close'] / latest['ema_5']) - 1) * 100, 2),
                'Resistance_Break': round(((latest['close'] / latest['resistance']) - 1) * 100, 2)
            })
            
            processed += 1
            if processed % 100 == 0:
                print(f"Processed {processed} symbols...")

        except Exception as e:
            errors += 1
            continue
    
    if not results:
        print("No momentum opportunities found!")
        return
    
    # Create DataFrame and sort by composite score
    results_df = pd.DataFrame(results)
    results_df = results_df.sort_values('Composite_Score', ascending=False).reset_index(drop=True)
    results_df['Rank'] = range(1, len(results_df) + 1)
    
    # Save results
    output_path = os.path.join(predictions_folder, 'short_term_momentum_scan.csv')
    results_df.to_csv(output_path, index=False)
    
    print(f"\nShort-Term Momentum Scan Complete!")
    print(f"Results saved to: {output_path}")
    print(f"Total symbols processed: {len(symbols)}")
    print(f"Symbols with sufficient data: {processed}")
    print(f"Symbols skipped (filters): {skipped}")
    print(f"Symbols with errors: {errors}")
    print(f"Final opportunities found: {len(results_df)}")
    
    # Display top 25 results
    print("\n" + "="*120)
    print("TOP 25 SHORT-TERM MOMENTUM OPPORTUNITIES (1-3 DAYS)")
    print("="*120)
    
    top_25 = results_df.head(25)
    display_cols = ['Rank', 'Symbol', 'Composite_Score', 'Current_Price', 'Change_1D', 
                   'Change_2D', 'Volume_Ratio', 'RSI', 'Price_vs_EMA5']
    print(top_25[display_cols].to_string(index=False))
    
    print(f"\nFocus on stocks with:")
    print(f"- High Volume_Ratio (>1.5)")
    print(f"- Positive Change_1D and Change_2D")
    print(f"- RSI between 40-70")
    print(f"- Price_vs_EMA5 > 0 (above 5-day EMA)")
    
    return results_df

if __name__ == '__main__':
    scan_short_term_momentum()
