#!/Users/<USER>/.local/share/uv/tools/aider-chat/bin/python3
"""
Automated Fyers authentication using TOTP (Time-based One-Time Password)
This eliminates manual login and automatically saves tokens
"""

import os
import pyotp
import requests
from fyers_apiv3 import fyersModel
import json
from datetime import datetime

class FyersAutoAuth:
    def __init__(self):
        self.app_id = "JMHITXL31I-100"
        self.app_secret = "58XM4NGA9U"
        self.redirect_uri = "http://127.0.0.1:5000/fyers/callback"
        self.token_file = "fyers_token.json"
        
        # TOTP settings (you'll need to set these up)
        self.username = os.getenv("FYERS_USERNAME")  # Your Fyers login ID
        self.password = os.getenv("FYERS_PASSWORD")  # Your Fyers password
        self.totp_secret = os.getenv("FYERS_TOTP_SECRET")  # TOTP secret from Fyers
        
    def setup_totp_instructions(self):
        """Instructions to set up TOTP for automated authentication"""
        print("🔧 TOTP Setup Instructions:")
        print("1. Login to Fyers web platform")
        print("2. Go to Profile > Two Factor Authentication")
        print("3. Enable TOTP and scan QR code with authenticator app")
        print("4. Get the TOTP secret key (usually shown as text)")
        print("5. Set environment variables:")
        print("   export FYERS_USERNAME='your_fyers_username'")
        print("   export FYERS_PASSWORD='your_fyers_password'")
        print("   export FYERS_TOTP_SECRET='your_totp_secret_key'")
        
    def generate_totp(self):
        """Generate TOTP code"""
        if not self.totp_secret:
            return None
        totp = pyotp.TOTP(self.totp_secret)
        return totp.now()
    
    def load_saved_token(self):
        """Load previously saved token"""
        if os.path.exists(self.token_file):
            try:
                with open(self.token_file, 'r') as f:
                    data = json.load(f)
                    
                # Check if token is still valid (basic check)
                token = data.get('access_token')
                if token:
                    # Test token validity
                    fyers = fyersModel.FyersModel(
                        token=token, 
                        is_async=False, 
                        client_id=self.app_id, 
                        log_path=""
                    )
                    
                    profile = fyers.get_profile()
                    if profile.get('code') == 200:
                        print("✅ Using saved valid token")
                        return token
                    else:
                        print("⚠️  Saved token expired, generating new one...")
                        
            except Exception as e:
                print(f"⚠️  Error loading saved token: {e}")
        
        return None
    
    def save_token(self, access_token):
        """Save token with metadata"""
        token_data = {
            'access_token': access_token,
            'created_at': datetime.now().isoformat(),
            'app_id': self.app_id
        }
        
        with open(self.token_file, 'w') as f:
            json.dump(token_data, f, indent=2)
        
        print(f"✅ Token saved to {self.token_file}")
    
    def automated_login(self):
        """Perform automated login using TOTP"""
        if not all([self.username, self.password, self.totp_secret]):
            print("❌ Missing credentials for automated login")
            self.setup_totp_instructions()
            return None
        
        try:
            # Generate TOTP code
            totp_code = self.generate_totp()
            print(f"🔐 Generated TOTP: {totp_code}")
            
            # Create session
            session = fyersModel.SessionModel(
                client_id=self.app_id,
                secret_key=self.app_secret,
                redirect_uri=self.redirect_uri,
                response_type="code",
                grant_type="authorization_code"
            )
            
            # This is where you'd implement the automated browser interaction
            # For now, we'll use the manual method but with better UX
            auth_url = session.generate_authcode()
            
            print("🤖 Automated authentication in progress...")
            print("📱 TOTP Code ready:", totp_code)
            print("🌐 Auth URL:", auth_url)
            
            # TODO: Implement selenium/playwright for full automation
            print("\n⚠️  For full automation, you'll need to:")
            print("1. Install selenium: pip install selenium")
            print("2. Use automated browser interaction")
            print("3. Or use the manual method below")
            
            return self.manual_auth_with_totp(session, totp_code)
            
        except Exception as e:
            print(f"❌ Automated login failed: {e}")
            return None
    
    def manual_auth_with_totp(self, session, totp_code):
        """Manual authentication with TOTP code provided"""
        auth_url = session.generate_authcode()
        
        print(f"\n🔐 Your TOTP code: {totp_code}")
        print("📋 Steps:")
        print("1. Open this URL:", auth_url)
        print("2. Login with username/password")
        print(f"3. Enter TOTP code: {totp_code}")
        print("4. Copy the redirected URL and paste below")
        
        redirected_url = input("\nPaste redirected URL: ").strip()
        
        if redirected_url:
            try:
                session.set_token(redirected_url)
                response = session.generate_token()
                
                if "access_token" in response:
                    access_token = response["access_token"]
                    self.save_token(access_token)
                    return access_token
                else:
                    print(f"❌ Token generation failed: {response}")
                    
            except Exception as e:
                print(f"❌ Authentication error: {e}")
        
        return None
    
    def get_authenticated_client(self):
        """Get authenticated Fyers client"""
        # Try to load saved token first
        token = self.load_saved_token()
        
        if not token:
            # Generate new token
            token = self.automated_login()
        
        if token:
            return fyersModel.FyersModel(
                token=token, 
                is_async=False, 
                client_id=self.app_id, 
                log_path=""
            )
        
        return None

def main():
    print("🤖 Fyers Automated Authentication")
    print("=" * 40)
    
    auth = FyersAutoAuth()
    
    # Get authenticated client
    fyers = auth.get_authenticated_client()
    
    if fyers:
        print("✅ Authentication successful!")
        
        # Test the connection
        profile = fyers.get_profile()
        if profile.get('code') == 200:
            print(f"👤 Logged in as: {profile.get('data', {}).get('name', 'N/A')}")
            print("🎉 Ready for automated trading!")
        else:
            print(f"⚠️  Profile test failed: {profile}")
    else:
        print("❌ Authentication failed")

if __name__ == "__main__":
    main()
