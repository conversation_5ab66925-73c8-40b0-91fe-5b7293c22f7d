{% extends "base.html" %}

{% block content %}
<div class="flex justify-between items-center mb-4">
    <h1 class="text-3xl font-bold">Predictions</h1>
    <div class="form-control">
        <form id="filter-form" method="get" action="/predictions">
            <select name="filter" class="select select-bordered" onchange="document.getElementById('filter-form').submit()">
                <option {% if filter_option == 'TOP 10' %}selected{% endif %}>TOP 10</option>
                <option {% if filter_option == 'TOP 20' %}selected{% endif %}>TOP 20</option>
                <option {% if filter_option == 'TOP 50' %}selected{% endif %}>TOP 50</option>
                <option {% if filter_option == 'TOP 100' %}selected{% endif %}>TOP 100</option>
                <option {% if filter_option == 'ALL' %}selected{% endif %}>ALL</option>
            </select>
        </form>
    </div>
</div>

<table class="table table-zebra w-full">
    <thead>
        <tr>
            <th>Rank</th> <!-- Add Rank header -->
            <th>Stock</th>
            <th>30 day Closing</th>
            <th>30 day returns</th>
            <th>LTP</th> <!-- Add LTP header -->
            <th>Chart</th> <!-- Add Chart header -->
        </tr>
    </thead>
    <tbody>
        {% for prediction in predictions %}
        <tr>
            <td>{{ prediction.Rank }}</td> <!-- Add Rank data -->
            <td>{{ prediction.Stock }}</td>
            <td>{{ prediction['30 day Closing'] }}</td>
            <td>{{ prediction['30 day returns'] }}</td>
            <td>{{ prediction.LTP }}</td> <!-- Add LTP data -->
            <td>
                <a href="/charts?symbol={{ prediction.Stock }}" class="text-blue-500">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z" />
                    </svg>
                </a>
            </td> <!-- Add Chart icon data -->
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}
