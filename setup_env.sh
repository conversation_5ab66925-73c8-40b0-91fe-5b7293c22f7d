#!/bin/bash
# Setup script for Automated Momentum Trader environment variables

echo "=== Setting up Fyers API Environment Variables ==="
echo

# Set Fyers credentials
export FYERS_APP_ID="JMHITXL31I-100"
export FYERS_APP_SECRET="58XM4NGA9U"
export FYERS_REDIRECT_URI="http://127.0.0.1:5000/fyers/callback"

# Set trading parameters
export TRADING_CAPITAL="500000"

# Optional: Set Telegram credentials (uncomment and fill if you want notifications)
# export TG_BOT_TOKEN="your_telegram_bot_token"
# export TG_CHAT_ID="your_telegram_chat_id"

echo "✅ Environment variables set:"
echo "   FYERS_APP_ID: $FYERS_APP_ID"
echo "   FYERS_APP_SECRET: $FYERS_APP_SECRET"
echo "   FYERS_REDIRECT_URI: $FYERS_REDIRECT_URI"
echo "   TRADING_CAPITAL: $TRADING_CAPITAL"
echo

echo "To make these permanent, add the following lines to your ~/.bashrc or ~/.zshrc:"
echo "export FYERS_APP_ID=\"JMHITXL31I-100\""
echo "export FYERS_APP_SECRET=\"58XM4NGA9U\""
echo "export FYERS_REDIRECT_URI=\"http://127.0.0.1:5000/fyers/callback\""
echo "export TRADING_CAPITAL=\"500000\""
echo

echo "Now you can run your automated momentum trader:"
echo "python automated_momentum_trader.py"
