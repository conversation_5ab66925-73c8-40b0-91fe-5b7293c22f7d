#!/Users/<USER>/.local/share/uv/tools/aider-chat/bin/python3
"""
Complete the Fyers authentication process
"""

import os
from fyers_apiv3 import fyersModel

def complete_authentication():
    """Complete the authentication process with the redirected URL"""
    print("=== Fyers Authentication Completion ===\n")
    
    # Get credentials
    app_id = "JMHITXL31I-100"
    app_secret = "58XM4NGA9U"
    redirect_uri = "http://127.0.0.1:5000/fyers/callback"
    
    print("1. Open this URL in your browser (if not already open):")
    print("https://api-t1.fyers.in/api/v3/generate-authcode?client_id=JMHITXL31I-100&redirect_uri=http%3A%2F%2F127.0.0.1%3A5000%2Ffyers%2Fcallback&response_type=code&state=None")
    print("\n2. Login with your Fyers credentials")
    print("3. After successful login, you'll be redirected to a URL like:")
    print("   http://127.0.0.1:5000/fyers/callback?auth_code=XXXXXXXXXX&state=None")
    print("\n4. Copy that complete redirected URL and paste it below:")
    
    # Get the redirected URL from user
    redirected_url = input("\nPaste the complete redirected URL here: ").strip()
    
    if not redirected_url:
        print("❌ No URL provided. Exiting.")
        return
    
    if not redirected_url.startswith("http://127.0.0.1:5000/fyers/callback"):
        print("❌ Invalid URL format. Please make sure you copied the complete redirected URL.")
        return
    
    try:
        # Create session
        session = fyersModel.SessionModel(
            client_id=app_id,
            secret_key=app_secret,
            redirect_uri=redirect_uri,
            response_type="code",
            grant_type="authorization_code"
        )
        
        print("\n⏳ Processing authentication...")
        
        # Set the token from redirected URL
        session.set_token(redirected_url)
        
        # Generate access token
        response = session.generate_token()
        
        if "access_token" in response:
            access_token = response["access_token"]
            print("✅ Authentication successful!")
            print(f"Access Token: {access_token[:20]}...")
            
            # Save token to file
            with open("fyers_token.txt", "w") as f:
                f.write(access_token)
            print("✅ Token saved to fyers_token.txt")
            
            # Test the token by creating FyersModel
            fyers = fyersModel.FyersModel(
                token=access_token, 
                is_async=False, 
                client_id=app_id, 
                log_path=""
            )
            
            # Test with profile API
            print("\n⏳ Testing token with profile API...")
            profile = fyers.get_profile()
            
            if profile.get("code") == 200:
                print("✅ Token is working correctly!")
                print(f"Profile: {profile.get('data', {}).get('name', 'N/A')}")
                print("\n🎉 You can now run your automated momentum trader:")
                print("python automated_momentum_trader.py")
            else:
                print(f"⚠️  Token test failed: {profile}")
                
        else:
            print(f"❌ Authentication failed: {response}")
            
    except Exception as e:
        print(f"❌ Error during authentication: {e}")
        print("\nTroubleshooting:")
        print("- Make sure you copied the complete redirected URL")
        print("- Check that the URL contains 'auth_code=' parameter")
        print("- Verify your credentials are correct")

if __name__ == "__main__":
    complete_authentication()
