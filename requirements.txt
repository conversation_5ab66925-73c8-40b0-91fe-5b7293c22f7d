alembic==1.13.2
appnope==0.1.4
APScheduler==3.10.4
asttokens==2.4.1
attrs==23.2.0
bcrypt==4.1.3
beautifulsoup4==4.12.3
blinker==1.8.2
catboost==1.2.5
certifi==2024.6.2
charset-normalizer==3.3.2
click==8.1.7
colorama==0.4.6
comm==0.2.2
contourpy==1.2.1
cycler==0.12.1
debugpy==1.8.2
decorator==5.1.1
Deprecated==1.2.14
dnspython==2.6.1
email_validator==2.2.0
executing==2.0.1
fastjsonschema==2.20.0
Flask==3.0.3
Flask-APScheduler==1.13.1
Flask-Bcrypt==1.0.1
Flask-Limiter==3.7.0
Flask-Login==0.6.3
Flask-Mail==0.10.0
Flask-Migrate==4.0.7
Flask-SQLAlchemy==3.1.1
Flask-WTF==1.2.1
fonttools==4.53.1
frozendict==2.4.4
graphviz==0.20.3
greenlet==3.0.3
html5lib==1.1
idna==3.7
importlib_resources==6.4.0
ipykernel==6.29.5
ipython==8.26.0
itsdangerous==2.2.0
jedi==0.19.1
Jinja2==3.1.4
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter_client==8.6.2
jupyter_core==5.7.2
kiwisolver==1.4.5
limits==3.13.0
lxml==5.2.2
Mako==1.3.5
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.8.4
matplotlib-inline==0.1.7
mdurl==0.1.2
multitasking==0.0.11
nbformat==5.10.4
nest-asyncio==1.6.0
numpy==1.26.2
ordered-set==4.1.0
packaging==24.1
pandas==2.2.2
pandas_ta==0.3.14b0
parso==0.8.4
peewee==3.17.5
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.2.2
plotly==5.22.0
prompt_toolkit==3.0.47
psutil==6.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
Pygments==2.18.0
pyparsing==3.1.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.1
pyzmq==26.0.3
referencing==0.35.1
requests==2.32.3
rich==13.7.1
rpds-py==0.19.1
scikit-learn==1.5.1
scipy==1.14.0
setuptools==71.1.0
six==1.16.0
soupsieve==2.5
SQLAlchemy==2.0.31
stack-data==0.6.3
tenacity==8.4.2
threadpoolctl==3.5.0
tornado==6.4.1
tqdm==4.66.4
traitlets==5.14.3
typing_extensions==4.12.2
tzdata==2024.1
tzlocal==5.2
urllib3==2.2.2
wcwidth==0.2.13
webencodings==0.5.1
Werkzeug==3.0.3
wrapt==1.16.0
WTForms==3.1.2
yfinance==0.2.40
