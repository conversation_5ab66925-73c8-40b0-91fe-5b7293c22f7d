#!/bin/bash

echo "🤖 FYERS AUTOMATION SETUP"
echo "========================="
echo ""

echo "This script will help you set up FULLY AUTOMATED authentication for <PERSON>yers."
echo "After setup, you won't need to manually login anymore!"
echo ""

echo "📋 SETUP STEPS:"
echo ""

echo "1️⃣  TOTP Setup (for automated 2FA):"
echo "   - Login to Fyers web platform"
echo "   - Go to Profile > Two Factor Authentication"
echo "   - Enable TOTP and scan QR code with Google Authenticator"
echo "   - Get the TOTP secret key (usually shown as text below QR)"
echo ""

echo "2️⃣  Set Environment Variables:"
echo "   We'll add these to your shell profile for permanent setup"
echo ""

# Get user input
read -p "Enter your Fyers Username: " FYERS_USERNAME
read -s -p "Enter your Fyers Password: " FYERS_PASSWORD
echo ""
read -p "Enter your TOTP Secret Key (from step 1): " FYERS_TOTP_SECRET
echo ""

# Validate inputs
if [[ -z "$FYERS_USERNAME" || -z "$FYERS_PASSWORD" || -z "$FYERS_TOTP_SECRET" ]]; then
    echo "❌ All fields are required. Please run the script again."
    exit 1
fi

echo "3️⃣  Saving Configuration..."

# Create environment file
cat > fyers_automation.env << EOF
# Fyers Automation Configuration
# Generated on $(date)

export FYERS_APP_ID="JMHITXL31I-100"
export FYERS_APP_SECRET="58XM4NGA9U"
export FYERS_REDIRECT_URI="http://127.0.0.1:5000/fyers/callback"
export TRADING_CAPITAL="500000"

# Automation Credentials
export FYERS_USERNAME="$FYERS_USERNAME"
export FYERS_PASSWORD="$FYERS_PASSWORD"
export FYERS_TOTP_SECRET="$FYERS_TOTP_SECRET"

echo "🤖 Fyers automation environment loaded"
EOF

echo "✅ Configuration saved to fyers_automation.env"
echo ""

echo "4️⃣  Adding to Shell Profile..."

# Detect shell and add to profile
SHELL_PROFILE=""
if [[ "$SHELL" == *"zsh"* ]]; then
    SHELL_PROFILE="$HOME/.zshrc"
elif [[ "$SHELL" == *"bash"* ]]; then
    SHELL_PROFILE="$HOME/.bashrc"
else
    SHELL_PROFILE="$HOME/.profile"
fi

# Add source line to shell profile
SOURCE_LINE="source $(pwd)/fyers_automation.env"
if ! grep -q "$SOURCE_LINE" "$SHELL_PROFILE" 2>/dev/null; then
    echo "" >> "$SHELL_PROFILE"
    echo "# Fyers Automation" >> "$SHELL_PROFILE"
    echo "$SOURCE_LINE" >> "$SHELL_PROFILE"
    echo "✅ Added to $SHELL_PROFILE"
else
    echo "✅ Already configured in $SHELL_PROFILE"
fi

echo ""
echo "5️⃣  Installing Required Packages..."
/Users/<USER>/.local/share/uv/tools/aider-chat/bin/python -m pip install pyotp selenium webdriver-manager

echo ""
echo "🎉 SETUP COMPLETE!"
echo "=================="
echo ""
echo "📋 What's Ready:"
echo "✅ TOTP automation configured"
echo "✅ Environment variables set"
echo "✅ Required packages installed"
echo "✅ Shell profile updated"
echo ""
echo "🚀 Next Steps:"
echo "1. Restart your terminal (or run: source $SHELL_PROFILE)"
echo "2. Run your trading script: python automated_momentum_trader.py"
echo "3. Authentication will be MUCH easier now!"
echo ""
echo "💡 Features Enabled:"
echo "🔐 Auto-generated TOTP codes"
echo "🌐 Auto-opening browser"
echo "💾 Auto-saving tokens"
echo "🔄 Auto-reusing saved tokens"
echo ""
echo "🔒 Security Note:"
echo "Your credentials are stored locally in fyers_automation.env"
echo "Keep this file secure and don't share it!"
echo ""
echo "🎯 For FULL automation (no manual steps), you can optionally:"
echo "   pip install selenium webdriver-manager"
echo "   Then use: python fully_automated_auth.py"
