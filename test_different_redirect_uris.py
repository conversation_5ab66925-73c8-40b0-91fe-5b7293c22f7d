#!/Users/<USER>/.local/share/uv/tools/aider-chat/bin/python3
"""
Test different redirect URI formats to find the correct one
"""

import os
from fyers_apiv3 import fyersModel

def test_redirect_uri(redirect_uri, description):
    """Test a specific redirect URI"""
    print(f"\n=== Testing {description} ===")
    print(f"Redirect URI: {redirect_uri}")
    
    try:
        app_id = "JMHITXL31I-100"
        app_secret = "58XM4NGA9U"
        
        session = fyersModel.SessionModel(
            client_id=app_id,
            secret_key=app_secret,
            redirect_uri=redirect_uri,
            response_type="code",
            grant_type="authorization_code"
        )
        
        auth_url = session.generate_authcode()
        print(f"✅ Auth URL: {auth_url}")
        return auth_url
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return None

def main():
    print("=== Testing Different Redirect URI Formats ===")
    print("This will help identify the correct redirect URI format for your Fyers app.")
    
    # Common redirect URI formats
    redirect_uris = [
        ("https://127.0.0.1", "Standard localhost"),
        ("https://127.0.0.1/", "Localhost with trailing slash"),
        ("https://localhost", "Localhost domain"),
        ("https://localhost/", "Localhost domain with trailing slash"),
        ("http://127.0.0.1", "HTTP localhost (less secure)"),
        ("http://localhost", "HTTP localhost domain"),
        ("https://127.0.0.1:8080", "Localhost with port"),
        ("https://trade.fyers.in", "Fyers domain (if configured)"),
    ]
    
    successful_urls = []
    
    for redirect_uri, description in redirect_uris:
        auth_url = test_redirect_uri(redirect_uri, description)
        if auth_url:
            successful_urls.append((redirect_uri, auth_url))
    
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    
    if successful_urls:
        print("✅ Successfully generated auth URLs:")
        for i, (redirect_uri, auth_url) in enumerate(successful_urls, 1):
            print(f"\n{i}. Redirect URI: {redirect_uri}")
            print(f"   Auth URL: {auth_url}")
    else:
        print("❌ No successful auth URLs generated")
    
    print("\n" + "="*60)
    print("TROUBLESHOOTING STEPS")
    print("="*60)
    print("1. 📋 Check your Fyers API Dashboard:")
    print("   - Go to: https://myapi.fyers.in/dashboard/")
    print("   - Login with your Fyers credentials")
    print("   - Find your app: JMHITXL31I-100")
    print("   - Check if status is 'Active'")
    print("   - Note the exact Redirect URI configured")
    
    print("\n2. 🔍 Common Issues:")
    print("   - App not activated in dashboard")
    print("   - Redirect URI mismatch (case sensitive)")
    print("   - App ID typo")
    print("   - Using test environment vs live environment")
    
    print("\n3. 🛠️  If still getting 'invalid client id':")
    print("   - Double-check App ID: JMHITXL31I-100")
    print("   - Ensure you're using the correct environment (test vs live)")
    print("   - Contact Fyers support if app is not showing in dashboard")
    
    print("\n4. 📞 Fyers Support:")
    print("   - Email: <EMAIL>")
    print("   - Community: https://fyers.in/community/")

if __name__ == "__main__":
    main()
