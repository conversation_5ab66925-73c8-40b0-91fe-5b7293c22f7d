#!/Users/<USER>/.local/share/uv/tools/aider-chat/bin/python3
"""
Test script to verify Fyers API v3 authentication step by step
"""

import os
from fyers_apiv3 import fyersModel

def test_credentials():
    """Test if credentials are properly formatted"""
    print("=== Testing Fyers Credentials ===")
    
    # Get credentials from environment or use defaults
    app_id = os.getenv("FYERS_APP_ID", "JMHITXL31I-100")
    app_secret = os.getenv("FYERS_APP_SECRET", "58XM4NGA9U")
    redirect_uri = os.getenv("FYERS_REDIRECT_URI", "https://127.0.0.1")
    
    print(f"App ID: {app_id}")
    print(f"App Secret: {app_secret[:8]}...")  # Only show first 8 chars for security
    print(f"Redirect URI: {redirect_uri}")
    
    # Validate App ID format
    if not app_id.endswith("-100"):
        print("⚠️  Warning: App ID should end with '-100'")
        return False
    
    if len(app_id.split("-")[0]) != 10:
        print("⚠️  Warning: App ID format might be incorrect")
        return False
    
    print("✅ Credentials format looks correct")
    return True

def test_session_creation():
    """Test SessionModel creation"""
    print("\n=== Testing SessionModel Creation ===")
    
    try:
        app_id = os.getenv("FYERS_APP_ID", "JMHITXL31I-100")
        app_secret = os.getenv("FYERS_APP_SECRET", "58XM4NGA9U")
        redirect_uri = os.getenv("FYERS_REDIRECT_URI", "https://127.0.0.1")
        
        # Create session with exact parameters from documentation
        session = fyersModel.SessionModel(
            client_id=app_id,
            secret_key=app_secret,
            redirect_uri=redirect_uri,
            response_type="code",
            grant_type="authorization_code"
        )
        
        print("✅ SessionModel created successfully")
        return session
        
    except Exception as e:
        print(f"❌ SessionModel creation failed: {e}")
        return None

def test_auth_url_generation(session):
    """Test authentication URL generation"""
    print("\n=== Testing Auth URL Generation ===")
    
    try:
        auth_url = session.generate_authcode()
        print("✅ Auth URL generated successfully")
        print(f"Auth URL: {auth_url}")
        
        # Check if URL looks valid
        if "https://api-t1.fyers.in/api/v3/generate-authcode" in auth_url:
            print("✅ Auth URL format looks correct")
            return auth_url
        else:
            print("⚠️  Auth URL format might be incorrect")
            return auth_url
            
    except Exception as e:
        print(f"❌ Auth URL generation failed: {e}")
        return None

def main():
    print("=== Fyers API v3 Authentication Test ===\n")
    
    # Test 1: Credentials
    if not test_credentials():
        print("\n❌ Credential test failed. Please check your App ID and Secret.")
        return
    
    # Test 2: Session creation
    session = test_session_creation()
    if not session:
        print("\n❌ Session creation failed.")
        return
    
    # Test 3: Auth URL generation
    auth_url = test_auth_url_generation(session)
    if not auth_url:
        print("\n❌ Auth URL generation failed.")
        return
    
    print("\n=== Test Summary ===")
    print("✅ All tests passed!")
    print("\n📋 Next Steps:")
    print("1. Copy the auth URL above and open it in your browser")
    print("2. Login with your Fyers credentials")
    print("3. After successful login, you'll be redirected to a URL starting with your redirect_uri")
    print("4. Copy that complete redirected URL and use it in your main script")
    print("\n💡 If you get 'invalid client id' error in browser:")
    print("   - Double-check your App ID in Fyers API dashboard")
    print("   - Ensure the App is in 'Active' status")
    print("   - Verify the redirect URI matches exactly")
    
    # Additional debugging info
    print(f"\n🔍 Debug Info:")
    print(f"   Expected App ID format: XXXXXXXXXX-100")
    print(f"   Your App ID: {os.getenv('FYERS_APP_ID', 'JMHITXL31I-100')}")
    print(f"   Redirect URI: {os.getenv('FYERS_REDIRECT_URI', 'https://127.0.0.1')}")

if __name__ == "__main__":
    main()
