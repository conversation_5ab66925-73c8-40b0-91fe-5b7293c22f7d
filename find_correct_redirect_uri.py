#!/Users/<USER>/.local/share/uv/tools/aider-chat/bin/python3
"""
Test different redirect URIs to find the correct one configured in Fyers dashboard
"""

from fyers_apiv3 import fyersModel

def test_redirect_uri(redirect_uri, description):
    """Test a specific redirect URI"""
    print(f"\n🧪 Testing: {description}")
    print(f"📍 URI: {redirect_uri}")
    
    try:
        session = fyersModel.SessionModel(
            client_id="JMHITXL31I-100",
            secret_key="58XM4NGA9U",
            redirect_uri=redirect_uri,
            response_type="code",
            grant_type="authorization_code"
        )
        
        auth_url = session.generate_authcode()
        print(f"✅ SUCCESS: {auth_url}")
        return auth_url
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return None

def main():
    print("🔍 FINDING CORRECT REDIRECT URI")
    print("=" * 50)
    print("This will test common redirect URIs to find the one configured in your Fyers dashboard.")
    print()
    
    # Common redirect URIs used by Fyers
    test_uris = [
        ("https://trade.fyers.in/api-login/redirect-to-app", "Official Fyers redirect"),
        ("https://api.fyers.in/api/v2/token", "API v2 token endpoint"),
        ("https://api-t1.fyers.in/api/v3/token", "API v3 token endpoint"),
        ("http://127.0.0.1:5000/fyers/callback", "Local callback (original)"),
        ("https://127.0.0.1", "HTTPS localhost"),
        ("http://127.0.0.1", "HTTP localhost"),
        ("https://localhost", "HTTPS localhost domain"),
        ("http://localhost", "HTTP localhost domain"),
        ("https://myapi.fyers.in/dashboard/", "Fyers dashboard"),
        ("https://trade.fyers.in", "Fyers trading platform"),
        ("urn:ietf:wg:oauth:2.0:oob", "Out-of-band (desktop apps)"),
    ]
    
    working_uris = []
    
    for uri, desc in test_uris:
        auth_url = test_redirect_uri(uri, desc)
        if auth_url:
            working_uris.append((uri, auth_url))
    
    print("\n" + "=" * 50)
    print("📊 RESULTS")
    print("=" * 50)
    
    if working_uris:
        print("✅ Working redirect URIs found:")
        for i, (uri, auth_url) in enumerate(working_uris, 1):
            print(f"\n{i}. {uri}")
            print(f"   Auth URL: {auth_url}")
        
        print("\n🎯 RECOMMENDED ACTION:")
        print("1. Try the first working URI above")
        print("2. Update your script with that URI")
        print("3. Test authentication")
        
        # Show how to update
        best_uri = working_uris[0][0]
        print(f"\n📝 UPDATE YOUR SCRIPT:")
        print(f'REDIRECT_URI = "{best_uri}"')
        
    else:
        print("❌ No working redirect URIs found!")
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Check your Fyers API dashboard:")
        print("   https://myapi.fyers.in/dashboard/")
        print("2. Verify your app is ACTIVE")
        print("3. Note the exact redirect URI configured")
        print("4. Make sure App ID is correct: JMHITXL31I-100")
        
    print("\n💡 MANUAL CHECK:")
    print("If none work, please:")
    print("1. Login to https://myapi.fyers.in/dashboard/")
    print("2. Find your app: JMHITXL31I-100")
    print("3. Copy the exact 'Redirect URI' shown there")
    print("4. Use that URI in your script")

if __name__ == "__main__":
    main()
